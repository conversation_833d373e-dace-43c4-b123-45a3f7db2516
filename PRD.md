# 📄 Product Requirements Document (PRD)

**Title:** SEO Article-Centric Content Repurposing System with Enhanced MCP Integration
**Owner:** \[Iskandar <PERSON>i / Yield Sight System]
**Date:** 2025-07-27
**Version:** 2.0 (Enhanced with Comprehensive MCP Integration & Strategic Topic Framework)

---

## 1. 🎯 Objective

To create a streamlined, repeatable content creation system where a long-form **SEO article** is written first, then repurposed for major platforms: **LinkedIn**, **Threads**, **X (Twitter)**, and **Facebook**. Each content piece must comply with platform-specific guidelines outlined in its respective PRD. Final outputs are saved into a structured folder system for easy access and publishing. Must be based on real facts/research/statistics and not made up stories/experience.

**Enhanced Objectives:**
- Mandate comprehensive utilization of ALL available MCP tools for maximum content effectiveness
- Implement strategic 80/20 evergreen/trending content framework
- Establish pillar content expansion strategy with topic cluster mapping
- Ensure systematic trend research and topic identification

---

## 2. 🧩 Scope

This workflow focuses on **content creation, formatting, and strategic topic development**. Uploading, scheduling, and analytics are out of scope.

**Enhanced Scope:**
- Comprehensive MCP tool integration across all content creation phases
- Strategic topic selection using trend research and SEO analysis
- Pillar content development with systematic expansion planning
- Content calendar development with evergreen/trending balance

---

## 3. 🔧 Comprehensive MCP Tool Integration Requirements

### Mandatory MCP Tool Utilization

**ALL content pieces MUST utilize the following MCP tools systematically:**

#### Phase 1: Research & Planning (Required Tools)
- **`sequential_thinking`**: Structure logic and content planning (minimum 5 thought steps)
- **`web-search`**: Factual research and trend verification (minimum 5 searches per topic)
- **`SEO-MCP tools`**: Keyword research and competitive analysis (minimum 3 tool uses)
- **`browser_navigate_Playwright`**: Direct website research and verification
- **`codebase-retrieval`**: Access existing content and framework templates

#### Phase 2: Content Creation (Required Tools)
- **`sequential_thinking`**: Content structure and optimization planning
- **`web-search`**: Real-time fact checking and statistics verification
- **`SEO-MCP tools`**: Ongoing keyword optimization and competitive monitoring
- **`str-replace-editor`**: Content creation and editing
- **`save-file`**: Systematic file creation and organization

#### Phase 3: Quality Assurance (Required Tools)
- **`view`**: Content review and verification
- **`diagnostics`**: Error checking and quality control
- **`web-search`**: Final fact verification and link validation
- **`SEO-MCP tools`**: Final SEO optimization verification

### Minimum Tool Usage Requirements by Content Type

**SEO Article (2,500+ words):**
- `sequential_thinking`: 8-12 thought sequences
- `web-search`: 10-15 research queries
- `SEO-MCP tools`: 5-8 optimization tasks
- `browser_navigate_Playwright`: 3-5 direct site visits
- `str-replace-editor`: Multiple editing sessions

**Social Media Posts (All Platforms):**
- `sequential_thinking`: 3-5 thought sequences per platform
- `web-search`: 2-3 trend verification searches
- `SEO-MCP tools`: 1-2 hashtag/keyword optimizations
- `view`: Content review and platform compliance check

**Email Outreach Campaign:**
- `sequential_thinking`: 5-7 strategy planning sequences
- `web-search`: 8-12 contact research and verification
- `browser_navigate_Playwright`: 5-10 website visits for contact verification
- `str-replace-editor`: Template customization and personalization

### MCP Tool Combination Strategies

**Research Amplification Strategy:**
1. `sequential_thinking` → Plan research approach
2. `web-search` → Gather initial data and trends
3. `SEO-MCP tools` → Analyze keyword opportunities
4. `browser_navigate_Playwright` → Verify sources and gather additional data
5. `sequential_thinking` → Synthesize findings and plan content

**Content Optimization Strategy:**
1. `sequential_thinking` → Structure content framework
2. `SEO-MCP tools` → Optimize for target keywords
3. `web-search` → Verify facts and add supporting data
4. `str-replace-editor` → Create and refine content
5. `view` → Review and quality check

**Trend Integration Strategy:**
1. `web-search` → Identify current trends and hot topics
2. `SEO-MCP tools` → Analyze trend search volume and competition
3. `sequential_thinking` → Plan trend integration approach
4. `browser_navigate_Playwright` → Research trending sources and data
5. `str-replace-editor` → Incorporate trending elements into content

---

## 4. 📊 Strategic Topic Selection Framework

### 80/20 Content Strategy Implementation

**80% Evergreen Content (Timeless Foundation):**
- Foundational industry knowledge and best practices
- How-to guides and implementation frameworks
- Case studies with lasting relevance
- Industry analysis and market insights
- Technology explanations and comparisons

**20% Trending Content (Current Relevance):**
- Breaking industry news and developments
- Seasonal topics and timely opportunities
- Emerging technology discussions
- Current event analysis and commentary
- Trending social media topics and hashtags

### Trend Research Requirements

**Mandatory Trend Research Sources (Use web-search to access):**
- Google Trends: Weekly trend analysis for target keywords
- Industry publications: Latest news and developments
- Social media platforms: Trending hashtags and discussions
- Competitor analysis: Recent content and engagement patterns
- News aggregators: Breaking industry news and updates

**Trend Identification Process:**
1. **`web-search`**: "trending topics [industry] 2025" (weekly)
2. **`SEO-MCP tools`**: Analyze trending keyword search volumes
3. **`browser_navigate_Playwright`**: Visit trending news sources
4. **`sequential_thinking`**: Evaluate trend relevance and longevity
5. **`web-search`**: Verify trend data and supporting statistics

### Specific Trend Research Sources

**Technology Trends:**
- TechCrunch, VentureBeat, Ars Technica
- GitHub trending repositories
- Product Hunt daily features
- Hacker News discussions

**Agricultural Technology Trends:**
- AgFunder News, Precision Agriculture Magazine
- Farm Technology News, AgTech Navigator
- USDA reports and agricultural statistics
- Climate and weather pattern analysis

**Government Technology Trends:**
- Government Technology Magazine
- Public Sector Technology publications
- Policy announcement tracking
- Digital transformation case studies

**Data Analytics Trends:**
- Analytics Insight, Data Science Central
- Kaggle competitions and datasets
- AI/ML research publications
- Business intelligence reports

---

## 5. 🏗️ Pillar Content Expansion Strategy

### Pillar Topic Development Framework

**Each Main Pillar Topic Must Generate Minimum 4 Related Subtopics:**

**Example Pillar Structure:**
```
Main Pillar: "Smart Farming ROI in Malaysia"
├── Subtopic 1: "Soil Sensor Implementation Guide"
├── Subtopic 2: "Weather Data Integration Strategies"
├── Subtopic 3: "Crop Monitoring Technology Comparison"
└── Subtopic 4: "Farm Management Software Selection"
```

### Topic Cluster Mapping Requirements

**Cluster Development Process:**
1. **`sequential_thinking`**: Plan pillar topic and identify subtopic opportunities
2. **`SEO-MCP tools`**: Research keyword clusters and search volume
3. **`web-search`**: Identify content gaps and competitor analysis
4. **`sequential_thinking`**: Map internal linking strategy
5. **`str-replace-editor`**: Document cluster plan and content calendar

**Internal Linking Strategy:**
- Pillar content links to all cluster subtopics
- Cluster content links back to pillar and related clusters
- Supporting content links to relevant clusters
- External authority links support all content levels

### Content Depth Levels

**Level 1 - Pillar Content (2,500-3,500 words):**
- Comprehensive topic overview
- Multiple subtopic introductions
- Authority-building depth and breadth
- Strong SEO optimization for head terms

**Level 2 - Cluster Content (1,500-2,500 words):**
- Specific subtopic deep-dive
- Practical implementation focus
- Long-tail keyword optimization
- Links to pillar and related clusters

**Level 3 - Supporting Content (800-1,500 words):**
- Specific questions and solutions
- Quick implementation guides
- FAQ-style content
- Very specific long-tail keywords

### Content Calendar Planning Framework

**Monthly Content Distribution:**
- Week 1: Pillar content publication
- Week 2: Cluster content #1 publication
- Week 3: Cluster content #2 publication
- Week 4: Supporting content and trending topic

**Quarterly Planning Cycle:**
- Month 1: Establish new pillar topic
- Month 2: Develop cluster content
- Month 3: Create supporting content and plan next pillar

**Annual Strategy:**
- 12 major pillar topics per year
- 48 cluster topics (4 per pillar)
- 24 trending topics (20% of content)
- 36 supporting pieces (3 per pillar)

---

## 6. 📌 Deliverables Per Topic

Each **topic** must have its own **folder** with the following files:

| File Name           | Description                           |
| ------------------- | ------------------------------------- |
| `seo-article.md`    | Long-form blog content, SEO optimized |
| `linkedin-post.txt` | Text for LinkedIn post                |
| `threads-post.txt`  | One-sentence relatable Threads post   |
| `x-post.txt`        | Engaging X (Twitter) post             |
| `facebook-post.txt` | Hook-driven Facebook post             |
| `backlink-email-outreach.md` | Email outreach campaign assets |
| `topic-cluster-plan.md` | Subtopic planning and internal linking strategy |

---

## 7. 🛠️ Master Content Creation & Repurposing Workflow

### 📋 Complete Process Overview (7-Day Cycle)

**Master Workflow Dependencies:**
```
Topic Selection → SEO Article → Platform Repurposing → Outreach Campaign → Quality Control → Publication
     ↓              ↓              ↓                    ↓                ↓              ↓
   Day 1-2        Day 3-4        Day 4-5              Day 5-6          Day 6-7        Day 7
```

**Required PRD Files (Process Order):**
1. **SEO-MCP-Workflow-Guide.md** → Research & optimization process
2. **SEO-article-PRD-Universal.md** → Master content creation
3. **LinkedIn-post-PRD-Universal.md** → Professional platform adaptation
4. **Threads-post-PRD-Universal.md** → Micro-content creation
5. **X-Twitter-post-PRD-Universal.md** → Thread and tweet development
6. **Facebook-post-PRD-Universal.md** → Story-driven content adaptation
7. **Backlink-Email-Outreach-Universal.md** → Authority building campaign

---

### 🔍 Phase 1: Strategic Foundation & Research (Days 1-2)

#### Day 1: Topic Selection & Strategic Planning

**Step 1.1: Topic Selection & Trend Analysis (2-3 hours)**
- **PRD Reference**: Follow topic selection framework from **SEO-article-PRD-Universal.md** Section 2
- **MCP Tools Required**: `sequential_thinking` (minimum 3 uses), `web-search` (minimum 5 searches)

**Process:**
1. **`sequential_thinking`**: Plan topic selection using 80/20 evergreen/trending strategy
2. **`web-search`**: Research trending topics in target industry
   - Search: "[INDUSTRY] trends 2025"
   - Search: "[INDUSTRY] challenges [CURRENT_YEAR]"
   - Search: "[INDUSTRY] innovation opportunities"
   - Search: "[INDUSTRY] market analysis [CURRENT_YEAR]"
   - Search: "[INDUSTRY] technology adoption statistics"
3. **`sequential_thinking`**: Evaluate topic potential against strategic criteria
4. **`sequential_thinking`**: Select final topic and define content variables

**Deliverable**: Topic selection with defined variables ([INDUSTRY_SECTOR], [AUDIENCE_TYPE], [CONTENT_CATEGORY])

**Quality Checkpoint**: ✅ Topic aligns with 80/20 strategy ✅ Variables clearly defined ✅ Strategic fit confirmed

---

**Step 1.2: SEO Research & Competitive Analysis (3-4 hours)**
- **PRD Reference**: Follow **SEO-MCP-Workflow-Guide.md** Phase 1 process
- **MCP Tools Required**: `SEO-MCP tools` (minimum 5 uses), `web-search` (minimum 5 searches), `browser_navigate_Playwright` (minimum 3 sites)

**Process:**
1. **`SEO-MCP tools`**: Execute keyword research sequence from SEO-MCP-Workflow-Guide.md
   - Primary keywords (3-5 terms)
   - Secondary keywords (5-8 terms)
   - Long-tail opportunities (8-12 terms)
2. **`SEO-MCP tools`**: Competitive analysis using competitor templates
   - Authority sites analysis
   - Direct competitors analysis
   - Backlink opportunities research
3. **`browser_navigate_Playwright`**: Visit top competitor sites for content gap analysis
4. **`web-search`**: Verify trending keywords and search volumes
5. **`sequential_thinking`**: Synthesize research into content strategy

**Deliverable**: Complete SEO research report with keyword targets and competitive positioning

**Quality Checkpoint**: ✅ Minimum 15 keywords researched ✅ 3+ competitors analyzed ✅ Content gaps identified

---

#### Day 2: Content Planning & Framework Development

**Step 2.1: Content Structure Planning (2-3 hours)**
- **PRD Reference**: Use **SEO-article-PRD-Universal.md** Section 3 article structure framework
- **MCP Tools Required**: `sequential_thinking` (minimum 4 uses), `codebase-retrieval` (minimum 2 uses)

**Process:**
1. **`codebase-retrieval`**: Access universal content templates and frameworks
2. **`sequential_thinking`**: Plan article structure using ATTENTION→WHY→HOW→WHAT→HOPE→SCARCITY→CTA framework
3. **`sequential_thinking`**: Adapt structure for chosen industry and audience
4. **`sequential_thinking`**: Plan platform repurposing strategy for all 4 social platforms
5. **`sequential_thinking`**: Identify pillar content expansion opportunities (minimum 4 subtopics)

**Deliverable**: Detailed content outline with platform adaptation plan

**Quality Checkpoint**: ✅ 7-section structure planned ✅ Platform adaptations outlined ✅ Subtopic expansion identified

---

**Step 2.2: Research Data Gathering (3-4 hours)**
- **PRD Reference**: Follow factual research requirements from **SEO-article-PRD-Universal.md**
- **MCP Tools Required**: `web-search` (minimum 10 searches), `browser_navigate_Playwright` (minimum 5 sites)

**Process:**
1. **`web-search`**: Gather factual data and statistics (minimum 10 searches)
   - Industry statistics and market data
   - Case studies and success stories
   - Expert quotes and authoritative sources
   - Current trends and developments
   - Supporting research and studies
2. **`browser_navigate_Playwright`**: Verify sources and gather additional data
   - Visit authoritative industry sites
   - Verify statistics and claims
   - Gather additional context and examples
3. **`sequential_thinking`**: Organize research findings by article section

**Deliverable**: Comprehensive research database with verified facts and sources

**Quality Checkpoint**: ✅ Minimum 10 factual sources ✅ All statistics verified ✅ Research organized by section

---

### ✍️ Phase 2: Master Content Creation (Days 3-4)

#### Day 3: SEO Article Development

**Step 3.1: Article Writing (4-6 hours)**
- **PRD Reference**: Follow **SEO-article-PRD-Universal.md** complete framework
- **MCP Tools Required**: `str-replace-editor` (multiple uses), `sequential_thinking` (minimum 3 uses), `web-search` (minimum 3 searches)

**Process:**
1. **`str-replace-editor`**: Create folder structure following enhanced folder template
2. **`sequential_thinking`**: Structure article outline with industry-specific adaptations
3. **`str-replace-editor`**: Write comprehensive SEO article (2,500-3,500 words)
   - Follow universal article structure framework
   - Apply industry-specific content modules
   - Include all required SEO elements (meta title, description, schema markup)
4. **`web-search`**: Real-time fact checking during writing process
5. **`view`**: Review content structure and flow

**Deliverable**: Complete SEO article (seo-article.md) with all optimization elements

**Quality Checkpoint**: ✅ 2,500+ words ✅ All SEO elements included ✅ Industry framework applied ✅ Facts verified

---

**Step 3.2: SEO Optimization & Enhancement (2-3 hours)**
- **PRD Reference**: Use **SEO-MCP-Workflow-Guide.md** optimization checklist
- **MCP Tools Required**: `SEO-MCP tools` (minimum 3 uses), `str-replace-editor` (multiple edits)

**Process:**
1. **`SEO-MCP tools`**: Optimize content for target keywords
2. **`SEO-MCP tools`**: Verify keyword density and placement
3. **`str-replace-editor`**: Implement SEO optimizations
4. **`SEO-MCP tools`**: Final SEO compliance check
5. **`view`**: Review optimized content

**Deliverable**: SEO-optimized article ready for repurposing

**Quality Checkpoint**: ✅ Target keywords optimized ✅ Meta elements complete ✅ Schema markup included ✅ Internal linking planned

---

#### Day 4: Platform Repurposing Development

**Step 4.1: LinkedIn Content Creation (1-2 hours)**
- **PRD Reference**: Follow **LinkedIn-post-PRD-Universal.md** complete framework
- **MCP Tools Required**: `sequential_thinking` (minimum 2 uses), `str-replace-editor` (1 file creation)

**Process:**
1. **`sequential_thinking`**: Plan LinkedIn adaptation using universal content format framework
2. **`sequential_thinking`**: Select appropriate post type and engagement goal
3. **`str-replace-editor`**: Create LinkedIn post using industry-adapted templates
4. **`view`**: Review for platform compliance and professional tone

**Deliverable**: LinkedIn post (linkedin-post.txt) optimized for professional audience

**Quality Checkpoint**: ✅ Professional tone maintained ✅ Industry adaptation applied ✅ Engagement mechanism included

---

**Step 4.2: Threads Content Creation (30 minutes)**
- **PRD Reference**: Follow **Threads-post-PRD-Universal.md** framework
- **MCP Tools Required**: `sequential_thinking` (minimum 1 use), `str-replace-editor` (1 file creation)

**Process:**
1. **`sequential_thinking`**: Identify core relatable truth from article
2. **`str-replace-editor`**: Create one-sentence Threads post using universal theme framework
3. **`view`**: Verify character count and relatability

**Deliverable**: Threads post (threads-post.txt) with maximum relatability

**Quality Checkpoint**: ✅ One sentence format ✅ Relatable truth identified ✅ Industry context maintained

---

**Step 4.3: X/Twitter Content Creation (1-2 hours)**
- **PRD Reference**: Follow **X-Twitter-post-PRD-Universal.md** framework
- **MCP Tools Required**: `sequential_thinking` (minimum 2 uses), `str-replace-editor` (1 file creation)

**Process:**
1. **`sequential_thinking`**: Plan thread structure using universal framework (6-12 tweets)
2. **`sequential_thinking`**: Adapt hook templates for industry and audience
3. **`str-replace-editor`**: Create Twitter thread using modular content templates
4. **`view`**: Review thread flow and engagement potential

**Deliverable**: Twitter/X thread (x-post.txt) optimized for engagement

**Quality Checkpoint**: ✅ Thread structure complete ✅ Industry hooks applied ✅ Engagement elements included

---

**Step 4.4: Facebook Content Creation (1-2 hours)**
- **PRD Reference**: Follow **Facebook-post-PRD-Universal.md** framework
- **MCP Tools Required**: `sequential_thinking` (minimum 2 uses), `str-replace-editor` (1 file creation)

**Process:**
1. **`sequential_thinking`**: Select appropriate content format and emotional tone
2. **`sequential_thinking`**: Plan story-driven adaptation for Facebook audience
3. **`str-replace-editor`**: Create Facebook post using universal content strategy framework
4. **`view`**: Review for accessibility and engagement mechanisms

**Deliverable**: Facebook post (facebook-post.txt) with story-driven approach

**Quality Checkpoint**: ✅ Story format applied ✅ Emotional connection created ✅ Engagement mechanisms included

---

### 📧 Phase 3: Authority Building & Outreach (Days 5-6)

#### Day 5: Email Outreach Campaign Development

**Step 5.1: Contact Research & Verification (2-3 hours)**
- **PRD Reference**: Use **Backlink-Email-Outreach-Universal.md** contact database and verification system
- **MCP Tools Required**: `web-search` (minimum 8 searches), `browser_navigate_Playwright` (minimum 5 sites)

**Process:**
1. **`web-search`**: Research potential contacts using industry-specific target lists
2. **`browser_navigate_Playwright`**: Verify contact information and website details
3. **`web-search`**: Research personalization elements for each contact
4. **`sequential_thinking`**: Plan outreach strategy and target prioritization

**Deliverable**: Verified contact list with personalization research

**Quality Checkpoint**: ✅ Minimum 15 contacts researched ✅ Contact information verified ✅ Personalization elements identified

---

**Step 5.2: Outreach Template Creation (2-3 hours)**
- **PRD Reference**: Follow **Backlink-Email-Outreach-Universal.md** email optimization criteria
- **MCP Tools Required**: `str-replace-editor` (1 file creation), `sequential_thinking` (minimum 2 uses)

**Process:**
1. **`sequential_thinking`**: Plan email campaign using optimization criteria (150-200 words, 2+ personalization elements)
2. **`str-replace-editor`**: Create outreach campaign using universal templates
3. **`sequential_thinking`**: Develop A/B testing variations for subject lines
4. **`view`**: Review for compliance with email optimization standards

**Deliverable**: Complete email outreach campaign (backlink-email-outreach.md)

**Quality Checkpoint**: ✅ Word count limits met ✅ Personalization requirements satisfied ✅ A/B testing planned

---

#### Day 6: Topic Cluster Planning & Documentation

**Step 6.1: Cluster Strategy Development (2-3 hours)**
- **PRD Reference**: Use pillar content expansion strategy from enhanced PRD framework
- **MCP Tools Required**: `SEO-MCP tools` (minimum 3 uses), `sequential_thinking` (minimum 3 uses)

**Process:**
1. **`sequential_thinking`**: Identify minimum 4 subtopic opportunities
2. **`SEO-MCP tools`**: Research cluster keyword opportunities
3. **`sequential_thinking`**: Plan internal linking strategy
4. **`str-replace-editor`**: Document cluster plan and content calendar

**Deliverable**: Topic cluster plan (topic-cluster-plan.md) with 4+ subtopics

**Quality Checkpoint**: ✅ Minimum 4 subtopics identified ✅ Keyword research complete ✅ Internal linking planned

---

**Step 6.2: MCP Usage Documentation (1 hour)**
- **PRD Reference**: Follow MCP documentation requirements from enhanced framework
- **MCP Tools Required**: `str-replace-editor` (1 file creation)

**Process:**
1. **`str-replace-editor`**: Create MCP tool usage log documenting all tool interactions
2. **`str-replace-editor`**: Create trend research notes documenting all research findings

**Deliverable**: MCP usage log (mcp-tool-usage-log.md) and trend research notes (trend-research-notes.md)

**Quality Checkpoint**: ✅ All MCP usage documented ✅ Research findings recorded ✅ Tool quotas verified

---

### ✅ Phase 4: Quality Assurance & Publication (Day 7)

#### Day 7: Final Review & Publication Preparation

**Step 7.1: Comprehensive Quality Review (2-3 hours)**
- **PRD Reference**: Cross-reference all platform PRD requirements
- **MCP Tools Required**: `view` (multiple files), `diagnostics` (error checking), `web-search` (final verification)

**Process:**
1. **`view`**: Review all content files for accuracy and consistency
   - SEO article compliance with SEO-article-PRD-Universal.md
   - LinkedIn post compliance with LinkedIn-post-PRD-Universal.md
   - Threads post compliance with Threads-post-PRD-Universal.md
   - X/Twitter post compliance with X-Twitter-post-PRD-Universal.md
   - Facebook post compliance with Facebook-post-PRD-Universal.md
   - Email outreach compliance with Backlink-Email-Outreach-Universal.md
2. **`diagnostics`**: Check for errors and formatting issues
3. **`web-search`**: Final fact verification and link validation
4. **`SEO-MCP tools`**: Final SEO optimization verification

**Deliverable**: Quality-assured content package ready for publication

**Quality Checkpoint**: ✅ All platform requirements met ✅ No errors detected ✅ Facts verified ✅ SEO optimized

---

**Step 7.2: Publication Setup & Monitoring (1-2 hours)**
- **MCP Tools Required**: `save-file` (final organization), `sequential_thinking` (publication planning)

**Process:**
1. **`save-file`**: Finalize all content files in proper folder structure
2. **`sequential_thinking`**: Plan publication schedule and monitoring approach
3. **`web-search`**: Set up monitoring for trending topics and engagement

**Deliverable**: Complete content package ready for publication with monitoring plan

**Final Quality Checkpoint**: ✅ All 7 files created ✅ Folder structure complete ✅ Publication plan ready ✅ Monitoring setup complete

---

## 8. 📂 Enhanced Folder Structure with Topic Clustering

### Individual Topic Folder Structure
```
📁 2025-07-27-smart-farming-roi-malaysian-estates/
├── seo-article.md
├── linkedin-post.txt
├── threads-post.txt
├── x-post.txt
├── facebook-post.txt
├── backlink-email-outreach.md
├── topic-cluster-plan.md
├── mcp-tool-usage-log.md
└── trend-research-notes.md
```

### Pillar Content Cluster Structure
```
📁 Content-Clusters/
├── 📁 Smart-Farming-Pillar/
│   ├── 📁 2025-07-27-smart-farming-roi-malaysian-estates/ (Pillar)
│   ├── 📁 2025-08-03-soil-sensor-implementation-guide/ (Cluster 1)
│   ├── 📁 2025-08-10-weather-data-integration-strategies/ (Cluster 2)
│   ├── 📁 2025-08-17-crop-monitoring-technology-comparison/ (Cluster 3)
│   └── 📁 2025-08-24-farm-management-software-selection/ (Cluster 4)
├── 📁 Government-IoT-Pillar/
│   ├── 📁 2025-09-01-government-iot-digital-transformation/ (Pillar)
│   └── [4 related cluster topics]
└── 📁 Agricultural-Data-Pillar/
    ├── 📁 2025-10-01-hidden-agricultural-data-patterns/ (Pillar)
    └── [4 related cluster topics]
```

---

## 9. 🔍 MCP Tool Usage Documentation Requirements

### Mandatory MCP Usage Logging

**Each topic folder MUST include `mcp-tool-usage-log.md` with:**

```markdown
# MCP Tool Usage Log

## Phase 1: Research & Planning
- `sequential_thinking`: [Number of uses] - [Brief description of each use]
- `web-search`: [Number of searches] - [List of search queries]
- `SEO-MCP tools`: [Tools used] - [Specific tasks performed]
- `browser_navigate_Playwright`: [Sites visited] - [Information gathered]

## Phase 2: Content Creation
- `sequential_thinking`: [Planning sequences used]
- `str-replace-editor`: [Files created/edited]
- `web-search`: [Fact-checking searches]
- `view`: [Content reviews performed]

## Phase 3: Quality Assurance
- `diagnostics`: [Error checks performed]
- `SEO-MCP tools`: [Final optimizations]
- `web-search`: [Final verifications]

## Total Tool Usage: [X tools used, Y total interactions]
```

### MCP Effectiveness Tracking

**Required Metrics per Topic:**
- Number of MCP tools utilized
- Total MCP interactions per content piece
- Research quality score (1-10)
- Content optimization score (1-10)
- Trend integration success (1-10)

---

## 10. ✅ Master Workflow Acceptance Criteria

### 📋 Process Completion Requirements

**Phase 1: Strategic Foundation (Days 1-2)**
- [ ] Topic selected using 80/20 evergreen/trending framework
- [ ] All content variables defined ([INDUSTRY_SECTOR], [AUDIENCE_TYPE], [CONTENT_CATEGORY])
- [ ] SEO research completed following SEO-MCP-Workflow-Guide.md
- [ ] Minimum 15 keywords researched with difficulty analysis
- [ ] 3+ competitors analyzed with traffic and backlink data
- [ ] Content gaps identified and documented
- [ ] Article structure planned using universal framework
- [ ] Platform repurposing strategy outlined for all 4 platforms
- [ ] Minimum 10 factual sources gathered and verified
- [ ] Research organized by article section

**Phase 2: Master Content Creation (Days 3-4)**
- [ ] SEO article created following SEO-article-PRD-Universal.md
- [ ] Article meets 2,500+ word minimum requirement
- [ ] All SEO elements included (meta title, description, schema markup)
- [ ] LinkedIn post created following LinkedIn-post-PRD-Universal.md
- [ ] Threads post created following Threads-post-PRD-Universal.md
- [ ] X/Twitter content created following X-Twitter-post-PRD-Universal.md
- [ ] Facebook post created following Facebook-post-PRD-Universal.md
- [ ] All platform-specific requirements met per respective PRDs

**Phase 3: Authority Building (Days 5-6)**
- [ ] Email outreach campaign created following Backlink-Email-Outreach-Universal.md
- [ ] Minimum 15 contacts researched and verified
- [ ] Email optimization criteria met (150-200 words, 2+ personalization elements)
- [ ] Topic cluster plan developed with minimum 4 subtopics
- [ ] Internal linking strategy mapped
- [ ] MCP tool usage documented in required format

**Phase 4: Quality Assurance (Day 7)**
- [ ] All content reviewed against respective PRD requirements
- [ ] No formatting errors or broken links detected
- [ ] All facts verified through multiple sources
- [ ] Final SEO optimization completed
- [ ] Publication plan and monitoring setup complete

### 🔧 MCP Tool Integration Compliance

**Mandatory Tool Usage Quotas (Per Topic):**
- [ ] `sequential_thinking`: Minimum 15 uses across all phases
- [ ] `web-search`: Minimum 25 searches for research and verification
- [ ] `SEO-MCP tools`: Minimum 8 optimization tasks
- [ ] `browser_navigate_Playwright`: Minimum 8 site visits
- [ ] `str-replace-editor`: All 9 content files created/edited
- [ ] `view`: Minimum 10 content review sessions
- [ ] `codebase-retrieval`: Minimum 2 template access sessions
- [ ] `diagnostics`: Final error checking completed

**Documentation Requirements:**
- [ ] MCP tool usage log (mcp-tool-usage-log.md) completed
- [ ] Trend research notes (trend-research-notes.md) documented
- [ ] All tool interactions logged with descriptions
- [ ] Tool effectiveness metrics recorded

### 📁 File Structure & Organization

**Required Files (9 total per topic):**
- [ ] `seo-article.md` - Master SEO-optimized article
- [ ] `linkedin-post.txt` - Professional platform content
- [ ] `threads-post.txt` - Micro-content for engagement
- [ ] `x-post.txt` - Thread format for Twitter/X
- [ ] `facebook-post.txt` - Story-driven social content
- [ ] `backlink-email-outreach.md` - Authority building campaign
- [ ] `topic-cluster-plan.md` - Expansion strategy documentation
- [ ] `mcp-tool-usage-log.md` - Tool usage documentation
- [ ] `trend-research-notes.md` - Research findings record

**Folder Structure Compliance:**
- [ ] Folder named using YYYY-MM-DD-topic-keyword format
- [ ] All files saved in correct format (.md or .txt as specified)
- [ ] Folder organized within appropriate pillar cluster structure

### 🎯 Content Quality Standards

**SEO Article Requirements:**
- [ ] 2,500-3,500 words with comprehensive coverage
- [ ] Universal article structure framework applied
- [ ] Industry-specific content modules integrated
- [ ] All SEO elements optimized (title, meta, schema, keywords)
- [ ] Factual accuracy verified through multiple sources
- [ ] No fabricated content or fictional scenarios

**Platform Content Requirements:**
- [ ] LinkedIn: Professional tone with authority building focus
- [ ] Threads: Single sentence with maximum relatability
- [ ] X/Twitter: 6-12 tweet thread with engagement hooks
- [ ] Facebook: Story-driven with emotional connection
- [ ] Email: Personalized outreach with verified contacts

**Universal Framework Application:**
- [ ] Dynamic variables correctly applied for industry/audience
- [ ] Contextual adaptation logic implemented
- [ ] Modular components appropriately selected
- [ ] Automatic personalization mechanisms activated

### 📊 Strategic Content Alignment

**80/20 Strategy Compliance:**
- [ ] Topic categorized as evergreen (80%) or trending (20%)
- [ ] Trend research conducted using specified sources
- [ ] Market timing and relevance validated
- [ ] Long-term value potential assessed

**Pillar Content Strategy:**
- [ ] Minimum 4 subtopic opportunities identified
- [ ] Content cluster mapping completed
- [ ] Internal linking strategy planned
- [ ] Content calendar integration scheduled

**Authority Building Focus:**
- [ ] Expertise positioning clearly established
- [ ] Unique perspective and insights demonstrated
- [ ] Credibility building elements included
- [ ] Thought leadership positioning achieved

### 🔍 Quality Control Checkpoints

**Phase Completion Gates:**
- [ ] Phase 1: Research foundation approved before content creation
- [ ] Phase 2: Master content approved before platform repurposing
- [ ] Phase 3: Platform content approved before outreach development
- [ ] Phase 4: All content approved before publication setup

**PRD Compliance Verification:**
- [ ] Each content piece verified against its specific PRD requirements
- [ ] Cross-platform consistency maintained
- [ ] Brand voice and messaging aligned across all formats
- [ ] Technical specifications met for each platform

**Final Publication Readiness:**
- [ ] All acceptance criteria met and verified
- [ ] Content package complete and organized
- [ ] Publication schedule planned and approved
- [ ] Monitoring and analytics setup configured

This master workflow ensures systematic, high-quality content creation with comprehensive MCP tool integration, strategic alignment, and platform optimization across all deliverables.
