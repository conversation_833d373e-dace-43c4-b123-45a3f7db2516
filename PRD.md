# 📄 Product Requirements Document (PRD)

**Title:** SEO Article-Centric Content Repurposing System
**Owner:** \[Iskandar Sulaili / Yield Sight System]
**Date:** 2025-07-26
**Version:** 1.0

---

## 1. 🎯 Objective

To create a streamlined, repeatable content creation system where a long-form **SEO article** is written first, then repurposed for major platforms: **LinkedIn**, **Threads**, **X (Twitter)**, and **Facebook**. Each content piece must comply with platform-specific guidelines outlined in its respective PRD. Final outputs are saved into a structured folder system for easy access and publishing. Must be based on real facts/research/statistics and not made up stories/experience.

---

## 2. 🧩 Scope

This workflow focuses only on **content creation and formatting**. Uploading, scheduling, and analytics are out of scope.

---

## 3. 📌 Deliverables Per Topic

Each **topic** must have its own **folder** with the following files:

| File Name           | Description                           |
| ------------------- | ------------------------------------- |
| `seo-article.md`    | Long-form blog content, SEO optimized |
| `linkedin-post.txt` | Text for LinkedIn post                |
| `threads-post.txt`  | One-sentence relatable Threads post   |
| `x-post.txt`        | Engaging X (Twitter) post             |
| `facebook-post.txt` | Hook-driven Facebook post             |

---

## 4. 🛠️ Workflow

1. **SEO Research & Optimization**

   * Use SEO-MCP tools for keyword research and competitive analysis
   * Follow SEO-MCP-Workflow-Guide.md for systematic optimization
   * Ensure article format, tone, keywords, and MCP usage align with SEO Article PRD

2. **Write the SEO Article**

   * Output in `seo-article.md`
   * Format: Markdown, ready for copy-paste into WordPress. (https://iskandarsulaili.com)
   * Tone: Authoritative but approachable.
   * Includes meta title, meta description, slug, H1, H2s, internal/external links, schema markup

3. **Repurpose for Platforms**

   * Follow the specific PRD of each platform listed in Section 6.
   * Output separate `.txt` files: LinkedIn, Threads, X, Facebook.

4. **Email Outreach Campaign**

   * Follow Email-Outreach-Backlink-PRD.md for systematic backlink acquisition
   * Create targeted outreach campaigns using the new content as anchor
   * Track backlink acquisition and relationship building metrics

5. **Save in Folder**

   * Folder naming convention: `YYYY-MM-DD-topic-keyword`
   * All five content files saved within that folder.
   * Email outreach assets saved in `backlink-email-outreach.md` within each folder

6. **Review & Quality Control**

   * Cross-check each post against its platform PRD.
   * Verify SEO optimization using SEO-MCP tools
   * Ensure email outreach campaigns are properly configured
   * Ensure no direct copy-paste unless platform PRD permits it.

---

## 5. 📂 Folder Structure Example

```
📁 2025-07-26-turn-data-into-profit
├── seo-article.md
├── linkedin-post.txt
├── threads-post.txt
├── x-post.txt
└── facebook-post.txt
```

---

* **MCP Usage Required:**

  * `sequential_thinking` to structure logic
  * `SEO-MCP tools` for keyword research and optimization
  * `web-search` for factual research and verification

## 6. ✅ Acceptance Criteria

* All 5 content pieces are generated and saved in the correct format
* All posts follow their respective PRD
* SEO optimization completed using SEO-MCP tools
* Email outreach campaign assets created following Email-Outreach-Backlink-PRD
* Folder and file naming are consistent
* SEO article meets on-page SEO standards with proper schema markup
* Backlink acquisition strategy implemented
* Proofread, no broken links or formatting issues
