# 📄 Product Requirements Document (PRD)

**Title:** SEO Article-Centric Content Repurposing System with Enhanced MCP Integration
**Owner:** \[Iskandar <PERSON>i / Yield Sight System]
**Date:** 2025-07-27
**Version:** 2.0 (Enhanced with Comprehensive MCP Integration & Strategic Topic Framework)

---

## 1. 🎯 Objective

To create a streamlined, repeatable content creation system where a long-form **SEO article** is written first, then repurposed for major platforms: **LinkedIn**, **Threads**, **X (Twitter)**, and **Facebook**. Each content piece must comply with platform-specific guidelines outlined in its respective PRD. Final outputs are saved into a structured folder system for easy access and publishing. Must be based on real facts/research/statistics and not made up stories/experience.

**Enhanced Objectives:**
- Mandate comprehensive utilization of ALL available MCP tools for maximum content effectiveness
- Implement strategic 80/20 evergreen/trending content framework
- Establish pillar content expansion strategy with topic cluster mapping
- Ensure systematic trend research and topic identification

---

## 2. 🧩 Scope

This workflow focuses on **content creation, formatting, and strategic topic development**. Uploading, scheduling, and analytics are out of scope.

**Enhanced Scope:**
- Comprehensive MCP tool integration across all content creation phases
- Strategic topic selection using trend research and SEO analysis
- Pillar content development with systematic expansion planning
- Content calendar development with evergreen/trending balance

---

## 3. 🔧 Comprehensive MCP Tool Integration Requirements

### Mandatory MCP Tool Utilization

**ALL content pieces MUST utilize the following MCP tools systematically:**

#### Phase 1: Research & Planning (Required Tools)
- **`sequential_thinking`**: Structure logic and content planning (minimum 5 thought steps)
- **`web-search`**: Factual research and trend verification (minimum 5 searches per topic)
- **`SEO-MCP tools`**: Keyword research and competitive analysis (minimum 3 tool uses)
- **`browser_navigate_Playwright`**: Direct website research and verification
- **`codebase-retrieval`**: Access existing content and framework templates

#### Phase 2: Content Creation (Required Tools)
- **`sequential_thinking`**: Content structure and optimization planning
- **`web-search`**: Real-time fact checking and statistics verification
- **`SEO-MCP tools`**: Ongoing keyword optimization and competitive monitoring
- **`str-replace-editor`**: Content creation and editing
- **`save-file`**: Systematic file creation and organization

#### Phase 3: Quality Assurance (Required Tools)
- **`view`**: Content review and verification
- **`diagnostics`**: Error checking and quality control
- **`web-search`**: Final fact verification and link validation
- **`SEO-MCP tools`**: Final SEO optimization verification

### Minimum Tool Usage Requirements by Content Type

**SEO Article (2,500+ words):**
- `sequential_thinking`: 8-12 thought sequences
- `web-search`: 10-15 research queries
- `SEO-MCP tools`: 5-8 optimization tasks
- `browser_navigate_Playwright`: 3-5 direct site visits
- `str-replace-editor`: Multiple editing sessions

**Social Media Posts (All Platforms):**
- `sequential_thinking`: 3-5 thought sequences per platform
- `web-search`: 2-3 trend verification searches
- `SEO-MCP tools`: 1-2 hashtag/keyword optimizations
- `view`: Content review and platform compliance check

**Email Outreach Campaign:**
- `sequential_thinking`: 5-7 strategy planning sequences
- `web-search`: 8-12 contact research and verification
- `browser_navigate_Playwright`: 5-10 website visits for contact verification
- `str-replace-editor`: Template customization and personalization

### MCP Tool Combination Strategies

**Research Amplification Strategy:**
1. `sequential_thinking` → Plan research approach
2. `web-search` → Gather initial data and trends
3. `SEO-MCP tools` → Analyze keyword opportunities
4. `browser_navigate_Playwright` → Verify sources and gather additional data
5. `sequential_thinking` → Synthesize findings and plan content

**Content Optimization Strategy:**
1. `sequential_thinking` → Structure content framework
2. `SEO-MCP tools` → Optimize for target keywords
3. `web-search` → Verify facts and add supporting data
4. `str-replace-editor` → Create and refine content
5. `view` → Review and quality check

**Trend Integration Strategy:**
1. `web-search` → Identify current trends and hot topics
2. `SEO-MCP tools` → Analyze trend search volume and competition
3. `sequential_thinking` → Plan trend integration approach
4. `browser_navigate_Playwright` → Research trending sources and data
5. `str-replace-editor` → Incorporate trending elements into content

---

## 4. 📊 Strategic Topic Selection Framework

### 80/20 Content Strategy Implementation

**80% Evergreen Content (Timeless Foundation):**
- Foundational industry knowledge and best practices
- How-to guides and implementation frameworks
- Case studies with lasting relevance
- Industry analysis and market insights
- Technology explanations and comparisons

**20% Trending Content (Current Relevance):**
- Breaking industry news and developments
- Seasonal topics and timely opportunities
- Emerging technology discussions
- Current event analysis and commentary
- Trending social media topics and hashtags

### Trend Research Requirements

**Mandatory Trend Research Sources (Use web-search to access):**
- Google Trends: Weekly trend analysis for target keywords
- Industry publications: Latest news and developments
- Social media platforms: Trending hashtags and discussions
- Competitor analysis: Recent content and engagement patterns
- News aggregators: Breaking industry news and updates

**Trend Identification Process:**
1. **`web-search`**: "trending topics [industry] 2025" (weekly)
2. **`SEO-MCP tools`**: Analyze trending keyword search volumes
3. **`browser_navigate_Playwright`**: Visit trending news sources
4. **`sequential_thinking`**: Evaluate trend relevance and longevity
5. **`web-search`**: Verify trend data and supporting statistics

### Specific Trend Research Sources

**Technology Trends:**
- TechCrunch, VentureBeat, Ars Technica
- GitHub trending repositories
- Product Hunt daily features
- Hacker News discussions

**Agricultural Technology Trends:**
- AgFunder News, Precision Agriculture Magazine
- Farm Technology News, AgTech Navigator
- USDA reports and agricultural statistics
- Climate and weather pattern analysis

**Government Technology Trends:**
- Government Technology Magazine
- Public Sector Technology publications
- Policy announcement tracking
- Digital transformation case studies

**Data Analytics Trends:**
- Analytics Insight, Data Science Central
- Kaggle competitions and datasets
- AI/ML research publications
- Business intelligence reports

---

## 5. 🏗️ Pillar Content Expansion Strategy

### Pillar Topic Development Framework

**Each Main Pillar Topic Must Generate Minimum 4 Related Subtopics:**

**Example Pillar Structure:**
```
Main Pillar: "Smart Farming ROI in Malaysia"
├── Subtopic 1: "Soil Sensor Implementation Guide"
├── Subtopic 2: "Weather Data Integration Strategies"
├── Subtopic 3: "Crop Monitoring Technology Comparison"
└── Subtopic 4: "Farm Management Software Selection"
```

### Topic Cluster Mapping Requirements

**Cluster Development Process:**
1. **`sequential_thinking`**: Plan pillar topic and identify subtopic opportunities
2. **`SEO-MCP tools`**: Research keyword clusters and search volume
3. **`web-search`**: Identify content gaps and competitor analysis
4. **`sequential_thinking`**: Map internal linking strategy
5. **`str-replace-editor`**: Document cluster plan and content calendar

**Internal Linking Strategy:**
- Pillar content links to all cluster subtopics
- Cluster content links back to pillar and related clusters
- Supporting content links to relevant clusters
- External authority links support all content levels

### Content Depth Levels

**Level 1 - Pillar Content (2,500-3,500 words):**
- Comprehensive topic overview
- Multiple subtopic introductions
- Authority-building depth and breadth
- Strong SEO optimization for head terms

**Level 2 - Cluster Content (1,500-2,500 words):**
- Specific subtopic deep-dive
- Practical implementation focus
- Long-tail keyword optimization
- Links to pillar and related clusters

**Level 3 - Supporting Content (800-1,500 words):**
- Specific questions and solutions
- Quick implementation guides
- FAQ-style content
- Very specific long-tail keywords

### Content Calendar Planning Framework

**Monthly Content Distribution:**
- Week 1: Pillar content publication
- Week 2: Cluster content #1 publication
- Week 3: Cluster content #2 publication
- Week 4: Supporting content and trending topic

**Quarterly Planning Cycle:**
- Month 1: Establish new pillar topic
- Month 2: Develop cluster content
- Month 3: Create supporting content and plan next pillar

**Annual Strategy:**
- 12 major pillar topics per year
- 48 cluster topics (4 per pillar)
- 24 trending topics (20% of content)
- 36 supporting pieces (3 per pillar)

---

## 6. 📌 Deliverables Per Topic

Each **topic** must have its own **folder** with the following files:

| File Name           | Description                           |
| ------------------- | ------------------------------------- |
| `seo-article.md`    | Long-form blog content, SEO optimized |
| `linkedin-post.txt` | Text for LinkedIn post                |
| `threads-post.txt`  | One-sentence relatable Threads post   |
| `x-post.txt`        | Engaging X (Twitter) post             |
| `facebook-post.txt` | Hook-driven Facebook post             |
| `backlink-email-outreach.md` | Email outreach campaign assets |
| `topic-cluster-plan.md` | Subtopic planning and internal linking strategy |

---

## 7. 🛠️ Enhanced Workflow with Comprehensive MCP Integration

### Phase 1: Strategic Planning & Research (Days 1-2)

**Step 1: Topic Selection & Trend Analysis**
- **`sequential_thinking`**: Plan topic selection approach and criteria
- **`web-search`**: Research trending topics in target industry (minimum 5 searches)
- **`SEO-MCP tools`**: Analyze keyword opportunities and competition
- **`browser_navigate_Playwright`**: Visit trending news sources and competitor sites
- **`sequential_thinking`**: Evaluate topic potential and strategic fit

**Step 2: Comprehensive Research & Data Gathering**
- **`web-search`**: Gather factual data, statistics, and case studies (minimum 10 searches)
- **`browser_navigate_Playwright`**: Verify sources and gather additional data
- **`SEO-MCP tools`**: Conduct competitive analysis and keyword research
- **`sequential_thinking`**: Synthesize research findings and plan content structure
- **`codebase-retrieval`**: Access existing content templates and frameworks

### Phase 2: Content Creation (Days 3-4)

**Step 3: SEO Article Development**
- **`sequential_thinking`**: Structure article outline and key points
- **`str-replace-editor`**: Create comprehensive SEO article (2,500+ words)
- **`web-search`**: Real-time fact checking and additional research
- **`SEO-MCP tools`**: Optimize content for target keywords
- **`view`**: Review content structure and quality

**Step 4: Platform Repurposing**
- **`sequential_thinking`**: Plan platform-specific adaptations
- **`str-replace-editor`**: Create LinkedIn, Threads, X, and Facebook posts
- **`view`**: Review each post for platform compliance
- **`web-search`**: Verify trending hashtags and platform best practices

### Phase 3: Outreach & Optimization (Days 5-6)

**Step 5: Email Outreach Campaign Development**
- **`sequential_thinking`**: Plan outreach strategy and target identification
- **`web-search`**: Research potential contacts and publications
- **`browser_navigate_Playwright`**: Verify contact information and website details
- **`str-replace-editor`**: Create personalized outreach templates
- **`view`**: Review outreach materials for quality and compliance

**Step 6: Topic Cluster Planning**
- **`sequential_thinking`**: Identify subtopic opportunities and internal linking strategy
- **`SEO-MCP tools`**: Research cluster keyword opportunities
- **`str-replace-editor`**: Document cluster plan and content calendar
- **`save-file`**: Save all content files in organized folder structure

### Phase 4: Quality Assurance & Publication (Day 7)

**Step 7: Comprehensive Review**
- **`view`**: Review all content files for accuracy and consistency
- **`diagnostics`**: Check for errors and formatting issues
- **`web-search`**: Final fact verification and link validation
- **`SEO-MCP tools`**: Final SEO optimization check

**Step 8: Publication & Monitoring Setup**
- **`save-file`**: Finalize all content files in proper folder structure
- **`sequential_thinking`**: Plan publication schedule and monitoring approach
- **`web-search`**: Set up monitoring for trending topics and engagement

---

## 8. 📂 Enhanced Folder Structure with Topic Clustering

### Individual Topic Folder Structure
```
📁 2025-07-27-smart-farming-roi-malaysian-estates/
├── seo-article.md
├── linkedin-post.txt
├── threads-post.txt
├── x-post.txt
├── facebook-post.txt
├── backlink-email-outreach.md
├── topic-cluster-plan.md
├── mcp-tool-usage-log.md
└── trend-research-notes.md
```

### Pillar Content Cluster Structure
```
📁 Content-Clusters/
├── 📁 Smart-Farming-Pillar/
│   ├── 📁 2025-07-27-smart-farming-roi-malaysian-estates/ (Pillar)
│   ├── 📁 2025-08-03-soil-sensor-implementation-guide/ (Cluster 1)
│   ├── 📁 2025-08-10-weather-data-integration-strategies/ (Cluster 2)
│   ├── 📁 2025-08-17-crop-monitoring-technology-comparison/ (Cluster 3)
│   └── 📁 2025-08-24-farm-management-software-selection/ (Cluster 4)
├── 📁 Government-IoT-Pillar/
│   ├── 📁 2025-09-01-government-iot-digital-transformation/ (Pillar)
│   └── [4 related cluster topics]
└── 📁 Agricultural-Data-Pillar/
    ├── 📁 2025-10-01-hidden-agricultural-data-patterns/ (Pillar)
    └── [4 related cluster topics]
```

---

## 9. 🔍 MCP Tool Usage Documentation Requirements

### Mandatory MCP Usage Logging

**Each topic folder MUST include `mcp-tool-usage-log.md` with:**

```markdown
# MCP Tool Usage Log

## Phase 1: Research & Planning
- `sequential_thinking`: [Number of uses] - [Brief description of each use]
- `web-search`: [Number of searches] - [List of search queries]
- `SEO-MCP tools`: [Tools used] - [Specific tasks performed]
- `browser_navigate_Playwright`: [Sites visited] - [Information gathered]

## Phase 2: Content Creation
- `sequential_thinking`: [Planning sequences used]
- `str-replace-editor`: [Files created/edited]
- `web-search`: [Fact-checking searches]
- `view`: [Content reviews performed]

## Phase 3: Quality Assurance
- `diagnostics`: [Error checks performed]
- `SEO-MCP tools`: [Final optimizations]
- `web-search`: [Final verifications]

## Total Tool Usage: [X tools used, Y total interactions]
```

### MCP Effectiveness Tracking

**Required Metrics per Topic:**
- Number of MCP tools utilized
- Total MCP interactions per content piece
- Research quality score (1-10)
- Content optimization score (1-10)
- Trend integration success (1-10)

---

## 10. ✅ Enhanced Acceptance Criteria

### Content Quality Standards
- [ ] All 7 content pieces generated and saved in correct format
- [ ] All posts follow their respective PRD guidelines
- [ ] SEO optimization completed using minimum 5 SEO-MCP tool interactions
- [ ] Email outreach campaign assets created with verified contact information
- [ ] Topic cluster plan developed with 4+ subtopic opportunities identified
- [ ] Folder and file naming consistent with enhanced structure

### MCP Integration Requirements
- [ ] Minimum 8 different MCP tools utilized per topic
- [ ] Sequential thinking used minimum 8 times across all phases
- [ ] Web search conducted minimum 15 times for research and verification
- [ ] SEO-MCP tools used minimum 5 times for optimization
- [ ] Browser navigation used minimum 3 times for direct research
- [ ] MCP usage log completed and documented

### Strategic Content Requirements
- [ ] Topic aligns with 80/20 evergreen/trending strategy
- [ ] Trend research conducted using specified sources
- [ ] Pillar content expansion plan developed
- [ ] Internal linking strategy mapped
- [ ] Content calendar integration planned

### Quality Assurance Standards
- [ ] SEO article meets 2,500+ word minimum with proper schema markup
- [ ] All facts verified through multiple sources
- [ ] Backlink acquisition strategy implemented with verified contacts
- [ ] No broken links or formatting issues
- [ ] Platform-specific content optimized for each channel
- [ ] Trend integration appropriate and timely

### Performance Tracking Setup
- [ ] Success metrics defined for each content piece
- [ ] Monitoring systems configured for trend tracking
- [ ] Analytics setup for content performance measurement
- [ ] Relationship building metrics established for outreach campaigns

This enhanced PRD ensures systematic utilization of all available MCP tools while implementing a strategic approach to content creation that balances evergreen value with trending relevance, supported by comprehensive topic clustering and expansion planning.
